alabaster==0.7.12
anaconda-client==1.7.2
anaconda-project==0.8.3
argh==0.26.2
asn1crypto==1.3.0
astroid @ file:///C:/ci/astroid_1592495992870/work
astropy==4.0.1.post1
atomicwrites==1.4.0
attrs==19.3.0
autopep8 @ file:///tmp/build/80754af9/autopep8_1592412889138/work
Babel==2.8.0
backcall==0.2.0
backports.shutil-get-terminal-size==1.0.0
bcrypt==3.1.7
beautifulsoup4==4.9.1
bitarray @ file:///C:/ci/bitarray_1594753995860/work
bkcharts==0.2
bleach==3.1.5
bokeh @ file:///C:/ci/bokeh_1593187680264/work
boto==2.49.0
Bottleneck==1.3.2
brotlipy==0.7.0
certifi==2020.12.5
cffi==1.14.0
chardet==3.0.4
click==7.1.2
cloudpickle @ file:///tmp/build/80754af9/cloudpickle_1594141588948/work
clyent==1.2.2
colorama==0.4.3
comtypes==1.1.7
contextlib2==0.6.0.post1
contextvars==2.4
cryptography==2.9.2
cycler==0.10.0
Cython @ file:///C:/ci/cython_1594834059511/work
cytoolz==0.10.1
dask @ file:///tmp/build/80754af9/dask-core_1594156306305/work
decorator==4.4.2
defusedxml==0.6.0
diff-match-patch @ file:///tmp/build/80754af9/diff-match-patch_1594828741838/work
distributed @ file:///C:/ci/distributed_1594740075051/work
docutils==0.16
entrypoints==0.3
et-xmlfile==1.0.1
fastcache==1.1.0
filelock==3.0.12
flake8==3.8.3
Flask==1.1.2
fsspec==0.7.4
future==0.18.2
gevent @ file:///C:/ci/gevent_1593010720265/work
glob2==0.7
gmpy2==2.0.8
greenlet==0.4.16
h5py==2.10.0
HeapDict==1.0.1
html5lib @ file:///tmp/build/80754af9/html5lib_1593446221756/work
idna @ file:///tmp/build/80754af9/idna_1593446292537/work
imageio @ file:///tmp/build/80754af9/imageio_1594161405741/work
imagesize==1.2.0
immutables @ file:///C:/ci/immutables_1592426094604/work
importlib-metadata @ file:///C:/ci/importlib-metadata_1593431910144/work
intervaltree @ file:///tmp/build/80754af9/intervaltree_1594361675072/work
ipykernel @ file:///C:/ci/ipykernel_1594830088120/work/dist/ipykernel-5.3.2-py3-none-any.whl
ipython @ file:///C:/ci/ipython_1593446240034/work
ipython-genutils==0.2.0
ipywidgets==7.5.1
isort==4.3.21
itsdangerous==1.1.0
jdcal==1.4.1
jedi @ file:///C:/ci/jedi_1592841982010/work
Jinja2==2.11.2
joblib @ file:///tmp/build/80754af9/joblib_1594236160679/work
json5==0.9.5
jsonschema==3.2.0
jupyter==1.0.0
jupyter-client @ file:///tmp/build/80754af9/jupyter_client_1594826976318/work
jupyter-console==6.1.0
jupyter-core==4.6.3
jupyterlab==2.1.5
jupyterlab-server @ file:///tmp/build/80754af9/jupyterlab_server_1594164409481/work
keyring @ file:///C:/ci/keyring_1593109797081/work
kiwisolver==1.2.0
lazy-object-proxy==1.4.3
libarchive-c==2.9
llvmlite==0.33.0+1.g022ab0f
locket==0.2.0
lxml @ file:///C:/ci/lxml_1594821524941/work
MarkupSafe==1.1.1
matplotlib @ file:///C:/ci/matplotlib-base_1592846129657/work
mccabe==0.6.1
menuinst==1.4.16
mistune==0.8.4
mkl-fft==1.0.14
mkl-random==1.0.4
mkl-service==2.3.0
mock==4.0.2
more-itertools==8.4.0
mpmath==1.1.0
msgpack==1.0.0
multipledispatch==0.6.0
nbconvert==5.6.1
nbformat==5.0.7
networkx @ file:///tmp/build/80754af9/networkx_1594377231366/work
nltk @ file:///tmp/build/80754af9/nltk_1592496090529/work
nose==1.3.7
notebook==6.0.3
numba==0.50.1
numexpr==2.7.1
numpy==1.19.5
numpydoc @ file:///tmp/build/80754af9/numpydoc_1594166760263/work
olefile==0.46
opencv-contrib-python==********
opencv-python==********
openpyxl @ file:///tmp/build/80754af9/openpyxl_1594167385094/work
packaging==20.4
pandas @ file:///C:/ci/pandas_1608056614942/work
pandocfilters==1.4.2
paramiko==2.7.1
parso==0.7.0
partd==1.1.0
path==13.1.0
pathlib2==2.3.5
pathtools==0.1.2
patsy==0.5.1
pep8==1.7.1
pexpect==4.8.0
pickleshare==0.7.5
Pillow @ file:///C:/ci/pillow_1594299032796/work
pkginfo==*******
pluggy==0.13.1
ply==3.11
prometheus-client==0.8.0
prompt-toolkit==3.0.5
psutil==5.7.0
py @ file:///tmp/build/80754af9/py_1593446248552/work
pycodestyle==2.6.0
pycosat==0.6.3
pycparser @ file:///tmp/build/80754af9/pycparser_1594388511720/work
pycrypto==2.6.1
pycurl==7.43.0.5
pydocstyle @ file:///tmp/build/80754af9/pydocstyle_1592848020240/work
pyflakes==2.2.0
Pygments==2.6.1
pylint @ file:///C:/ci/pylint_1592499115540/work
PyNaCl @ file:///C:/ci/pynacl_1595009245871/work
pyodbc===4.0.0-unsupported
pyOpenSSL @ file:///tmp/build/80754af9/pyopenssl_1594392929924/work
pyparsing==2.4.7
pyreadline==2.1
pyrsistent==0.16.0
PySocks==1.7.1
pytest==5.4.3
python-dateutil==2.8.1
python-jsonrpc-server @ file:///tmp/build/80754af9/python-jsonrpc-server_1594397536060/work
python-language-server @ file:///C:/ci/python-language-server_1594159702692/work
pytz==2020.1
PyWavelets==1.1.1
pywin32==227
pywin32-ctypes==0.2.0
pywinpty==0.5.7
PyYAML==5.3.1
pyzmq==19.0.1
QDarkStyle==2.8.1
QtAwesome==0.7.2
qtconsole @ file:///tmp/build/80754af9/qtconsole_1592848611704/work
QtPy==1.9.0
regex @ file:///C:/ci/regex_1593422126140/work
requests @ file:///tmp/build/80754af9/requests_1592841827918/work
rope==0.17.0
Rtree==0.9.4
ruamel-yaml==0.15.87
scikit-image==0.16.2
scikit-learn @ file:///C:/ci/scikit-learn_1621370577507/work
scipy @ file:///C:/ci/scipy_1597675683670/work
seaborn==0.10.1
Send2Trash==1.5.0
simplegeneric==0.8.1
singledispatch==*******
six==1.15.0
snowballstemmer==2.0.0
sortedcollections==1.2.1
sortedcontainers==2.2.2
soupsieve==2.0.1
Sphinx @ file:///tmp/build/80754af9/sphinx_1594223420021/work
sphinxcontrib-applehelp==1.0.2
sphinxcontrib-devhelp==1.0.2
sphinxcontrib-htmlhelp==1.0.3
sphinxcontrib-jsmath==1.0.1
sphinxcontrib-qthelp==1.0.3
sphinxcontrib-serializinghtml==1.1.4
sphinxcontrib-websupport @ file:///tmp/build/80754af9/sphinxcontrib-websupport_1593446360927/work
spyder @ file:///C:/ci/spyder_1594825773579/work
spyder-kernels @ file:///C:/ci/spyder-kernels_1594744032862/work
SQLAlchemy @ file:///C:/ci/sqlalchemy_1593446399229/work
statsmodels==0.11.1
sympy @ file:///C:/ci/sympy_1594234721190/work
tables==3.6.1
tblib==1.6.0
terminado==0.8.3
testpath==0.4.4
threadpoolctl @ file:///tmp/tmp9twdgx9k/threadpoolctl-2.1.0-py3-none-any.whl
toml @ file:///tmp/build/80754af9/toml_1592853716807/work
toolz==0.10.0
tornado==6.0.4
tqdm @ file:///tmp/build/80754af9/tqdm_1593446365756/work
traitlets==4.3.3
typed-ast==1.4.1
typing-extensions @ file:///tmp/build/80754af9/typing_extensions_1592847887441/work
ujson==1.35
unicodecsv==0.14.1
urllib3==1.25.9
watchdog @ file:///C:/ci/watchdog_1593446204230/work
wcwidth @ file:///tmp/build/80754af9/wcwidth_1593447189090/work
webencodings==0.5.1
Werkzeug==1.0.1
widgetsnbextension==3.5.1
win-inet-pton==1.1.0
win-unicode-console==0.5
wincertstore==0.2
wrapt==1.11.2
xlrd==1.2.0
XlsxWriter==1.2.9
xlwings==0.19.5
xlwt==1.3.0
yapf @ file:///tmp/build/80754af9/yapf_1593528177422/work
zict==2.0.0
zipp==3.1.0
zope.event==4.4
zope.interface==4.7.1
