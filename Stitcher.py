﻿import numpy as np
import cv2
# from scipy.stats import mode
import time
import os
import glob
import copy
# import skimage.measure
# from numba import jit
import ImageUtility as Utility
import ImageFusion
import time

class ImageFeature():
    # 用来保存串行全局拼接中的第二张图像的特征点和描述子，为后续加速拼接使用，避免重复计算
    isBreak = True      # 判断是否上一次中断
    kps = None
    feature = None


class Stitcher(Utility.Method):
    '''
    图像拼接类，包括所有跟材料显微组织图像配准相关函数
    '''
    isColorMode = True  # 是否为彩色图像模式
    direction = 1       # 拼接方向：1-上，2-左，3-下，4-右
    directIncre = 1     # 拼接增长方向，可以为1, 0, -1
    fuseMethod = "notFuse"  # 图像融合方法
    phaseResponseThreshold = 0.15  # 相位相关响应阈值
    tempImageFeature = ImageFeature()  # 临时存储图像特征
    imageFusion = ImageFusion.ImageFusion()  # 图像融合对象

    def directionIncrease(self, direction):
        """
        功能：改变拼接搜索方向，通过direction和directIncre控制，使得范围保持在[1,4]
        参数：
            direction: 当前的方向
        返回：
            更新后的方向
        """
        direction += self.directIncre
        if direction == 5:
            direction = 1
        if direction == 0:
            direction = 4
        return direction

    def flowStitch(self, fileList, caculateOffsetMethod):
        """
        功能：序列拼接，从list的第一张拼接到最后一张，由于中间可能出现拼接失败，故记录截止文件索引
        参数：
            fileList: 图像地址序列
            caculateOffsetMethod: 计算偏移量方法
        返回：
            ((status, endfileIndex), stitchImage) - （（拼接状态，截止文件索引），拼接结果）
        """
        self.printAndWrite("Stitching the directory which have " + str(fileList[0]))
        fileNum = len(fileList)
        offsetList = []
        describtion = ""
        # 计算小图像的偏移量
        startTime = time.time()
        status = True
        endfileIndex = 0
        for fileIndex in range(0, fileNum - 1):
            self.printAndWrite("stitching " + str(fileList[fileIndex]) + " and " + str(fileList[fileIndex + 1]))
            imageA = cv2.imdecode(np.fromfile(fileList[fileIndex], dtype=np.uint8), cv2.IMREAD_GRAYSCALE)
            imageB = cv2.imdecode(np.fromfile(fileList[fileIndex + 1], dtype=np.uint8), cv2.IMREAD_GRAYSCALE)
            if caculateOffsetMethod == self.calculateOffsetForPhaseCorrleate:
                (status, offset) = self.calculateOffsetForPhaseCorrleate([fileList[fileIndex], fileList[fileIndex + 1]])
            else:
                (status, offset) = caculateOffsetMethod([imageA, imageB])
            if status == False:
                describtion = "  " + str(fileList[fileIndex]) + " and " + str(fileList[fileIndex+1]) + " can not be stitched"
                break
            else:
                offsetList.append(offset)
                endfileIndex = fileIndex + 1
        endTime = time.time()

        self.printAndWrite("The time of registering is " + str(endTime - startTime) + "s")

        # 开始拼接和融合
        self.printAndWrite("start stitching")
        startTime = time.time()
        stitchImage = self.getStitchByOffset(fileList, offsetList)
        endTime = time.time()
        self.printAndWrite("The time of fusing is " + str(endTime - startTime) + "s")

        if status == False:
            self.printAndWrite(describtion)
        return ((status, endfileIndex), stitchImage)

    def flowStitchWithMutiple(self, fileList, caculateOffsetMethod):
        """
        功能：多段序列拼接，从list的第一张拼接到最后一张，由于中间可能出现拼接失败，将分段拼接结果共同返回
        参数：
            fileList: 图像地址序列
            caculateOffsetMethod: 计算偏移量方法
        返回：
            拼接的图像list
        """
        result = []
        totalNum = len(fileList)
        startNum = 0
        while 1:
            (status, stitchResult) = self.flowStitch(fileList[startNum: totalNum], caculateOffsetMethod)
            result.append(stitchResult)
            self.tempImageFeature.isBreak = True
            if status[1] == 1:
                startNum = startNum + status[1] + 1
            else:
                startNum = startNum + status[1] + 1

            if startNum == totalNum:
                break
            if startNum == (totalNum - 1):
                if self.isColorMode:
                    result.append(cv2.imdecode(np.fromfile(fileList[startNum], dtype=np.uint8), cv2.IMREAD_COLOR))
                else:
                    result.append(cv2.imdecode(np.fromfile(fileList[startNum], dtype=np.uint8), cv2.IMREAD_GRAYSCALE))
                break
            self.printAndWrite("stitching Break, start from " + str(fileList[startNum]) + " again")
        return result

    def imageSetStitch(self, projectAddress, outputAddress, fileNum, caculateOffsetMethod, startNum = 1, fileExtension = "jpg", outputfileExtension = "jpg"):
        """
        功能：图像集拼接方法
        参数：
            projectAddress: 项目地址
            outputAddress: 输出地址
            fileNum: 共多少个文件
            caculateOffsetMethod: 计算偏移量方法
            startNum: 从第几个文件开始拼
            fileExtension: 输入文件扩展名
            outputfileExtension: 输出文件扩展名
        """
        for i in range(startNum, fileNum+1):
            fileAddress = projectAddress + "\\" + str(i) + "\\"
            fileList = glob.glob(fileAddress + "*." + fileExtension)
            if not os.path.exists(outputAddress):
                os.makedirs(outputAddress)
            Stitcher.outputAddress = outputAddress
            (status, result) = self.flowStitch(fileList, caculateOffsetMethod)
            self.tempImageFeature.isBreak = True
            cv2.imwrite(outputAddress + "\\stitching_result_" + str(i) + "." + outputfileExtension, result)
            if status == False:
                self.printAndWrite("stitching Failed")

    def imageSetStitchWithMutiple(self, projectAddress, outputAddress, fileNum, caculateOffsetMethod, startNum = 1, fileExtension = "jpg", outputfileExtension = "jpg"):
        """
        功能：图像集多段拼接方法
        参数：
            projectAddress: 项目地址
            outputAddress: 输出地址
            fileNum: 共多少个文件
            caculateOffsetMethod: 计算偏移量方法
            startNum: 从第几个文件开始拼
            fileExtension: 输入文件扩展名
            outputfileExtension: 输出文件扩展名
        """
        for i in range(startNum, fileNum+1):
            startTime = time.time()
            fileAddress = projectAddress + "\\" + str(i) + "\\"
            fileList = glob.glob(fileAddress + "*." + fileExtension)
            if not os.path.exists(outputAddress):
                os.makedirs(outputAddress)
            Stitcher.outputAddress = outputAddress
            result = self.flowStitchWithMutiple(fileList, caculateOffsetMethod)
            self.tempImageFeature.isBreak = True
            if len(result) == 1:
                cv2.imwrite(outputAddress + "\\stitching_result_" + str(i) + "." + outputfileExtension, result[0])
            else:
                for j in range(0, len(result)):
                    cv2.imwrite(outputAddress + "\\stitching_result_" + str(i) + "_" + str(j+1) + "." + outputfileExtension, result[j])
            endTime = time.time()
            print("Time Consuming for " + fileAddress + " is " + str(endTime - startTime))

    def calculateOffsetForPhaseCorrleate(self, dirAddress):
        """
        功能：采用相位相关法计算偏移量
        参数：
            dirAddress: 图像地址列表
        返回：
            (status, offset) - (状态，偏移量)
        """
        (dir1, dir2) = dirAddress
        offset = [0, 0]
        status = True
        offsetList = self.phase.phaseCorrelation(dir1, dir2)
        offset = []
        offset.append(np.int32(np.round(offsetList[1])))
        offset.append(np.int32(np.round(offsetList[0])))
        self.printAndWrite("  The offset of stitching: dx is " + str(offset[0]) + " dy is " + str(offset[1]))
        return (status, offset)

    def calculateOffsetForPhaseCorrleateIncre(self, images):
        """
        功能：采用相位相关法计算偏移量-考虑增长搜索区域
        参数：
            images: [imageA, imageB] - 待拼接的两张图像
        返回：
            (status, offset) - (状态，偏移量)
        """
        (imageA, imageB) = images
        offset = [0, 0]
        status = False
        maxI = (np.floor(0.5 / self.roiRatio) + 1).astype(int)+ 1
        iniDirection = self.direction
        localDirection = iniDirection
        for i in range(1, maxI):
            while(True):
                roiImageA = self.getROIRegionForIncreMethod(imageA, direction=localDirection, order="first", searchRatio = i * self.roiRatio)
                roiImageB = self.getROIRegionForIncreMethod(imageB, direction=localDirection, order="second", searchRatio = i * self.roiRatio)

                (offsetTemp, response) = cv2.phaseCorrelate(np.float64(roiImageA), np.float64(roiImageB))
                offset[0] = np.int32(offsetTemp[1])
                offset[1] = np.int32(offsetTemp[0])
                if response > self.phaseResponseThreshold:
                    status = True
                if status == True:
                    break
                else:
                    localDirection = self.directionIncrease(localDirection)
                if localDirection == iniDirection:
                    break
            if status == True:
                if localDirection == 1:
                    offset[0] = offset[0] + imageA.shape[0] - int(i * self.roiRatio * imageA.shape[0])
                elif localDirection == 2:
                    offset[1] = offset[1] + imageA.shape[1] - int(i * self.roiRatio * imageA.shape[1])
                elif localDirection == 3:
                    offset[0] = offset[0] - (imageB.shape[0] - int(i * self.roiRatio * imageB.shape[0]))
                elif localDirection == 4:
                    offset[1] = offset[1] - (imageB.shape[1] - int(i * self.roiRatio * imageB.shape[1]))
                self.direction = localDirection
                break
        if status == False:
            return (status, "  The two images can not match")
        elif status == True:
            self.printAndWrite("  The offset of stitching: dx is " + str(offset[0]) + " dy is " + str(offset[1]))
            return (status, offset)

    def calculateOffsetForFeatureSearch(self, images):
        """
        功能：采用特征搜索计算偏移量
        参数：
            images: [imageA, imageB] - 待拼接的两张图像
        返回：
            (status, offset) - (状态，偏移量)
        """
        (imageA, imageB) = images
        offset = [0, 0]
        status = False
        if self.isEnhance == True:
            if self.isClahe == True:
                clahe = cv2.createCLAHE(clipLimit=self.clipLimit, tileGridSize=(self.tileSize, self.tileSize))
                imageA = clahe.apply(imageA)
                imageB = clahe.apply(imageB)
            elif self.isClahe == False:
                imageA = cv2.equalizeHist(imageA)
                imageB = cv2.equalizeHist(imageB)
        # 获取特征点
        if self.tempImageFeature.isBreak == True:
            (kpsA, featuresA) = self.detectAndDescribe(imageA, featureMethod=self.featureMethod)
            (kpsB, featuresB) = self.detectAndDescribe(imageB, featureMethod=self.featureMethod)
            self.tempImageFeature.isBreak = False
            self.tempImageFeature.kps = kpsB
            self.tempImageFeature.feature = featuresB
        else:
            kpsA = self.tempImageFeature.kps
            featuresA = self.tempImageFeature.feature
            (kpsB, featuresB) = self.detectAndDescribe(imageB, featureMethod=self.featureMethod)
            self.tempImageFeature.isBreak = False
            self.tempImageFeature.kps = kpsB
            self.tempImageFeature.feature = featuresB
        if featuresA is not None and featuresB is not None:
            matches = self.matchDescriptors(featuresA, featuresB)
            if self.offsetCaculate == "mode":
                (status, offset) = self.getOffsetByMode(kpsA, kpsB, matches, offsetEvaluate = self.offsetEvaluate)
            elif self.offsetCaculate == "ransac":
                (status, offset, adjustH) = self.getOffsetByRansac(kpsA, kpsB, matches, offsetEvaluate = self.offsetEvaluate)
        if status == False:
            self.tempImageFeature.isBreak = True
            return (status, "  The two images can not match")
        elif status == True:
            self.tempImageFeature.isBreak = False
            self.printAndWrite("  The offset of stitching: dx is " + str(offset[0]) + " dy is " + str(offset[1]))
            return (status, offset)

    def calculateOffsetForFeatureSearchIncre(self, images):
        """
        功能：采用特征搜索计算偏移量-考虑增长搜索区域
        参数：
            images: [imageA, imageB] - 待拼接的两张图像
        返回：
            (status, offset) - (状态，偏移量)
        """
        (imageA, imageB) = images
        offset = [0, 0]
        status = False
        maxI = (np.floor(0.5 / self.roiRatio) + 1).astype(int)+ 1
        iniDirection = self.direction
        localDirection = iniDirection
        for i in range(1, maxI):
            while(True):
                roiImageA = self.getROIRegionForIncreMethod(imageA, direction=localDirection, order="first", searchRatio = i * self.roiRatio)
                roiImageB = self.getROIRegionForIncreMethod(imageB, direction=localDirection, order="second", searchRatio = i * self.roiRatio)

                if self.isEnhance == True:
                    if self.isClahe == True:
                        clahe = cv2.createCLAHE(clipLimit=self.clipLimit,tileGridSize=(self.tileSize, self.tileSize))
                        roiImageA = clahe.apply(roiImageA)
                        roiImageB = clahe.apply(roiImageB)
                    elif self.isClahe == False:
                        roiImageA = cv2.equalizeHist(roiImageA)
                        roiImageB = cv2.equalizeHist(roiImageB)
                kpsA, featuresA = self.detectAndDescribe(roiImageA, featureMethod=self.featureMethod)
                kpsB, featuresB = self.detectAndDescribe(roiImageB, featureMethod=self.featureMethod)
                if featuresA is not None and featuresB is not None:
                    matches = self.matchDescriptors(featuresA, featuresB)
                    if self.offsetCaculate == "mode":
                        (status, offset) = self.getOffsetByMode(kpsA, kpsB, matches, offsetEvaluate = self.offsetEvaluate)
                    elif self.offsetCaculate == "ransac":
                        (status, offset, adjustH) = self.getOffsetByRansac(kpsA, kpsB, matches, offsetEvaluate = self.offsetEvaluate)
                if status:
                    break
                else:
                    localDirection = self.directionIncrease(localDirection)
                if localDirection == iniDirection:
                    break
            if status:
                if localDirection == 1:
                    offset[0] = offset[0] + imageA.shape[0] - int(i * self.roiRatio * imageA.shape[0])
                elif localDirection == 2:
                    offset[1] = offset[1] + imageA.shape[1] - int(i * self.roiRatio * imageA.shape[1])
                elif localDirection == 3:
                    offset[0] = offset[0] - (imageB.shape[0] - int(i * self.roiRatio * imageB.shape[0]))
                elif localDirection == 4:
                    offset[1] = offset[1] - (imageB.shape[1] - int(i * self.roiRatio * imageB.shape[1]))
                self.direction = localDirection
                break
        if status == False:
            return (status, "  The two images can not match")
        elif status == True:
            self.printAndWrite("  The offset of stitching: dx is " + str(offset[0]) + " dy is " + str(offset[1]))
            return (status, offset)

    def getStitchByOffset(self, fileList, originOffsetList):
        """
        功能：通过偏移量列表和文件列表得到最终的拼接结果
        参数：
            fileList: 图像列表
            originOffsetList: 偏移量列表
        返回：
            ndarray - 拼接后的图像
        """
        dxSum = dySum = 0
        imageList = []
        if self.isColorMode:
            imageList.append(cv2.imdecode(np.fromfile(fileList[0], dtype=np.uint8), cv2.IMREAD_COLOR))
        else:
            imageList.append(cv2.imdecode(np.fromfile(fileList[0], dtype=np.uint8), cv2.IMREAD_GRAYSCALE))
        resultRow = imageList[0].shape[0]         # 拼接最终结果的横轴长度
        resultCol = imageList[0].shape[1]         # 拼接最终结果的纵轴长度
        originOffsetList.insert(0, [0, 0])        # 增加第一张图像相对于最终结果的原点的偏移量

        rangeX = [[0,0] for x in range(len(originOffsetList))]  # 记录X方向最大最小边界
        rangeY = [[0, 0] for x in range(len(originOffsetList))] # 记录Y方向最大最小边界
        offsetList = copy.deepcopy(originOffsetList)
        rangeX[0][1] = imageList[0].shape[0]
        rangeY[0][1] = imageList[0].shape[1]

        for i in range(1, len(offsetList)):
            if Stitcher.isColorMode:
                tempImage = cv2.imdecode(np.fromfile(fileList[i], dtype=np.uint8), cv2.IMREAD_COLOR)
            else:
                tempImage = cv2.imdecode(np.fromfile(fileList[i], dtype=np.uint8), cv2.IMREAD_GRAYSCALE)
            dxSum = dxSum + offsetList[i][0]
            dySum = dySum + offsetList[i][1]
            if dxSum <= 0:
                for j in range(0, i):
                    offsetList[j][0] = offsetList[j][0] + abs(dxSum)
                    rangeX[j][0] = rangeX[j][0] + abs(dxSum)
                    rangeX[j][1] = rangeX[j][1] + abs(dxSum)
                resultRow = resultRow + abs(dxSum)
                rangeX[i][1] = resultRow
                dxSum = rangeX[i][0] = offsetList[i][0] = 0
            else:
                offsetList[i][0] = dxSum
                resultRow = max(resultRow, dxSum + tempImage.shape[0])
                rangeX[i][1] = resultRow
            if dySum <= 0:
                for j in range(0, i):
                    offsetList[j][1] = offsetList[j][1] + abs(dySum)
                    rangeY[j][0] = rangeY[j][0] + abs(dySum)
                    rangeY[j][1] = rangeY[j][1] + abs(dySum)
                resultCol = resultCol + abs(dySum)
                rangeY[i][1] = resultCol
                dySum = rangeY[i][0] = offsetList[i][1] = 0
            else:
                offsetList[i][1] = dySum
                resultCol = max(resultCol, dySum + tempImage.shape[1])
                rangeY[i][1] = resultCol
            imageList.append(tempImage)
        stitchResult = None
        if self.isColorMode:
            stitchResult = np.zeros((resultRow, resultCol, 3), np.int32) - 1
        else:
            stitchResult = np.zeros((resultRow, resultCol), np.int32) - 1
        self.printAndWrite("  The rectified offsetList is " + str(offsetList))
        for i in range(0, len(offsetList)):
            self.printAndWrite("  stitching " + str(fileList[i]))
            if i == 0:
                if self.isColorMode:
                    stitchResult[offsetList[0][0]: offsetList[0][0] + imageList[0].shape[0], offsetList[0][1]: offsetList[0][1] + imageList[0].shape[1], :] = imageList[0]
                else:
                    stitchResult[offsetList[0][0]: offsetList[0][0] + imageList[0].shape[0], offsetList[0][1]: offsetList[0][1] + imageList[0].shape[1]] = imageList[0]
            else:
                if self.fuseMethod == "notFuse":
                    if self.isColorMode:
                        stitchResult[offsetList[i][0]: offsetList[i][0] + imageList[i].shape[0], offsetList[i][1]: offsetList[i][1] + imageList[i].shape[1], :] = imageList[i]
                    else:
                        stitchResult[offsetList[i][0]: offsetList[i][0] + imageList[i].shape[0], offsetList[i][1]: offsetList[i][1] + imageList[i].shape[1]] = imageList[i]
                else:
                    minOccupyX = rangeX[i-1][0]
                    maxOccupyX = rangeX[i-1][1]
                    minOccupyY = rangeY[i-1][0]
                    maxOccupyY = rangeY[i-1][1]
                    roi_ltx = max(offsetList[i][0], minOccupyX)
                    roi_lty = max(offsetList[i][1], minOccupyY)
                    roi_rbx = min(offsetList[i][0] + imageList[i].shape[0], maxOccupyX)
                    roi_rby = min(offsetList[i][1] + imageList[i].shape[1], maxOccupyY)

                    if self.isColorMode:
                        roiImageRegionA = stitchResult[roi_ltx:roi_rbx, roi_lty:roi_rby, :].copy()
                        stitchResult[offsetList[i][0]: offsetList[i][0] + imageList[i].shape[0], offsetList[i][1]: offsetList[i][1] + imageList[i].shape[1], :] = imageList[i]
                        roiImageRegionB = stitchResult[roi_ltx:roi_rbx, roi_lty:roi_rby, :].copy()
                        stitchResult[roi_ltx:roi_rbx, roi_lty:roi_rby, :] = self.fuseImage([roiImageRegionA, roiImageRegionB], originOffsetList[i][0], originOffsetList[i][1])
                    else:
                        roiImageRegionA = stitchResult[roi_ltx:roi_rbx, roi_lty:roi_rby].copy()
                        stitchResult[offsetList[i][0]: offsetList[i][0] + imageList[i].shape[0], offsetList[i][1]: offsetList[i][1] + imageList[i].shape[1]] = imageList[i]
                        roiImageRegionB = stitchResult[roi_ltx:roi_rbx, roi_lty:roi_rby].copy()
                        stitchResult[roi_ltx:roi_rbx, roi_lty:roi_rby] = self.fuseImage([roiImageRegionA, roiImageRegionB], originOffsetList[i][0], originOffsetList[i][1])
        stitchResult[stitchResult == -1] = 0
        return stitchResult.astype(np.uint8)

    def fuseImage(self, images, dx, dy):
        """
        功能：融合图像
        参数：
            images: [imageA, imageB] - 待融合的两张图像
            dx: x方向偏移量
            dy: y方向偏移量
        返回：
            融合后的图像区域
        """
        self.imageFusion.isColorMode = self.isColorMode
        (imageA, imageB) = images
        if self.fuseMethod != "fadeInAndFadeOut" and self.fuseMethod != "trigonometric":
            # 将各自区域中为背景的部分用另一区域填充，目的是消除背景
            # 权值为-1是为了方便渐入检出融合和三角融合计算
            imageA[imageA == -1] = 0
            imageB[imageB == -1] = 0
            imageA[imageA == 0] = imageB[imageA == 0]
            imageB[imageB == 0] = imageA[imageB == 0]

        fuseRegion = np.zeros(imageA.shape, np.uint8)
        if self.fuseMethod == "notFuse":
            fuseRegion = imageB
        elif self.fuseMethod == "average":
            fuseRegion = self.imageFusion.fuseByAverage([imageA, imageB])
        elif self.fuseMethod == "maximum":
            fuseRegion = self.imageFusion.fuseByMaximum([imageA, imageB])
        elif self.fuseMethod == "minimum":
            fuseRegion = self.imageFusion.fuseByMinimum([imageA, imageB])
        elif self.fuseMethod == "fadeInAndFadeOut":
            fuseRegion = self.imageFusion.fuseByFadeInAndFadeOut(images, dx, dy)
        elif self.fuseMethod == "trigonometric":
            fuseRegion = self.imageFusion.fuseByTrigonometric(images, dx, dy)
        elif self.fuseMethod == "multiBandBlending":
            assert self.isColorMode is False, "The multi Band Blending is not support for color mode in this code"
            fuseRegion = self.imageFusion.fuseByMultiBandBlending([imageA, imageB])
        elif self.fuseMethod == "optimalSeamLine":
            assert self.isColorMode is False, "The optimal seam line is not support for color mode in this code"
            fuseRegion = self.imageFusion.fuseByOptimalSeamLine(images, self.direction)
        return fuseRegion

if __name__=="__main__":
    stitcher = Stitcher()
    imageA = cv2.imread("D:\\images\\Image_SX04\\s_0047.jpg", 0)
    imageB = cv2.imread("D:\\images\\Image_SX04\\s_0048.jpg", 0)
    offset = stitcher.calculateOffsetForFeatureSearchIncre([imageA, imageB])