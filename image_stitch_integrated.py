#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像拼接整合版本 - 学习用途
整合了原项目的所有核心功能，去除了GPU加速部分
包含：图像特征检测、匹配、配准、拼接和融合等完整流程

原项目：ImageStitch
作者：整合版本用于学习
"""

import numpy as np
import cv2
import math
import time
import os
import glob
import copy


class ImageFeature:
    """用来保存串行全局拼接中的第二张图像的特征点和描述子，为后续加速拼接使用，避免重复计算"""
    isBreak = True      # 判断是否上一次中断
    kps = None
    feature = None


class ImageUtility:
    """图像处理工具类，包含特征检测、匹配等基础功能"""
    
    def __init__(self):
        # 关于打印信息的设置
        self.outputAddress = "result/"
        self.isEvaluate = False          # 是否输出到检验txt文件
        self.evaluateFile = "evaluate.txt"
        self.isPrintLog = True           # 是否在屏幕打印过程信息

        # 关于特征搜索的设置
        self.featureMethod = "surf"      # "sift","surf" or "orb"
        self.roiRatio = 0.1              # roi length for stitching in first direction
        self.searchRatio = 0.75          # 0.75 is common value for matches

        # 关于特征配准的设置
        self.offsetCaculate = "mode"     # "mode" or "ransac"
        self.offsetEvaluate = 3          # 3 means nums of matches for mode, 3.0 means threshold for ransac

        # 关于图像增强的操作
        self.isEnhance = False
        self.isClahe = False
        self.clipLimit = 20
        self.tileSize = 5

        # ORB参数设置
        self.orbNfeatures = 5000
        self.orbScaleFactor = 1.2
        self.orbNlevels = 8
        self.orbEdgeThreshold = 31
        self.orbFirstLevel = 0
        self.orbWTA_K = 2
        self.orbPatchSize = 31
        self.orbFastThreshold = 20

    def printAndWrite(self, content):
        """向屏幕和文件打印输出内容"""
        if self.isPrintLog:
            print(content)
        if self.isEvaluate:
            f = open(self.outputAddress + self.evaluateFile, "a")   # 在文件末尾追加
            f.write(content)
            f.write("\n")
            f.close()

    def getROIRegionForIncreMethod(self, image, direction=1, order="first", searchRatio=0.1):
        """
        对于搜索增长方法，根据比例获得其搜索区域
        :param image: 原始图像
        :param direction: 搜索方向 1-上，2-左，3-下，4-右
        :param order: 'first'or'second'判断属于第几张图像
        :param searchRatio: 裁剪搜素区域的比例，默认搜索方向上的长度的0.1
        :return: 搜索区域
        """
        row, col = image.shape[:2]
        roiRegion = np.zeros(image.shape, np.uint8)
        if direction == 1:  # 上
            searchLength = np.floor(row * searchRatio).astype(int)
            if order == "first":
                roiRegion = image[row - searchLength:row, :]
            elif order == "second":
                roiRegion = image[0: searchLength, :]
        elif direction == 2:  # 左
            searchLength = np.floor(col * searchRatio).astype(int)
            if order == "first":
                roiRegion = image[:, col - searchLength:col]
            elif order == "second":
                roiRegion = image[:, 0: searchLength]
        elif direction == 3:  # 下
            searchLength = np.floor(row * searchRatio).astype(int)
            if order == "first":
                roiRegion = image[0: searchLength, :]
            elif order == "second":
                roiRegion = image[row - searchLength:row, :]
        elif direction == 4:  # 右
            searchLength = np.floor(col * searchRatio).astype(int)
            if order == "first":
                roiRegion = image[:, 0: searchLength]
            elif order == "second":
                roiRegion = image[:, col - searchLength:col]
        return roiRegion

    def getOffsetByMode(self, kpsA, kpsB, matches, offsetEvaluate=10):
        """
        通过求众数的方法获得偏移量
        :param kpsA: 第一张图像的特征点
        :param kpsB: 第二张图像的特征点
        :param matches: 配准列表
        :param offsetEvaluate: 如果众数的个数大于本阈值，则配准正确，默认为10
        :return: 返回(totalStatus, [dx, dy]), totalStatus 是否正确，[dx, dy]默认[0, 0]
        """
        totalStatus = True
        if len(matches) == 0:
            totalStatus = False
            return (totalStatus, [0, 0])
        
        dxList = []
        dyList = []
        for trainIdx, queryIdx in matches:
            ptA = (kpsA[queryIdx][1], kpsA[queryIdx][0])
            ptB = (kpsB[trainIdx][1], kpsB[trainIdx][0])
            if int(ptA[0] - ptB[0]) == 0 and int(ptA[1] - ptB[1]) == 0:
                continue
            dxList.append(int(ptA[0] - ptB[0]))
            dyList.append(int(ptA[1] - ptB[1]))
        
        if len(dxList) == 0:
            dxList.append(0)
            dyList.append(0)
        
        # 获取众数偏移量
        zipped = zip(dxList, dyList)
        zip_list = list(zipped)
        zip_dict = dict((a, zip_list.count(a)) for a in zip_list)
        zip_dict_sorted = dict(sorted(zip_dict.items(), key=lambda x: x[1], reverse=True))

        dx = list(zip_dict_sorted)[0][0]
        dy = list(zip_dict_sorted)[0][1]
        num = zip_dict_sorted[list(zip_dict_sorted)[0]]

        if num < offsetEvaluate:
            totalStatus = False
        
        return (totalStatus, [dx, dy])

    def getOffsetByRansac(self, kpsA, kpsB, matches, offsetEvaluate=100):
        """
        通过RANSAC方法获得偏移量
        :param kpsA: 第一张图像的特征点
        :param kpsB: 第二张图像的特征点
        :param matches: 配准列表
        :param offsetEvaluate: 对于Ransac求属于最小范围的个数，大于本阈值，则正确
        :return: 返回(totalStatus, [dx, dy], adjustH), totalStatus 是否正确，[dx, dy]默认[0, 0]
        """
        totalStatus = False
        ptsA = np.float32([kpsA[i] for (_, i) in matches])
        ptsB = np.float32([kpsB[i] for (i, _) in matches])
        if len(matches) == 0:
            return (totalStatus, [0, 0], 0)
        
        # 计算单应性矩阵
        (H, status) = cv2.findHomography(ptsA, ptsB, cv2.RANSAC, 3, 0.9)
        trueCount = 0
        for i in range(0, len(status)):
            if status[i] == True:
                trueCount = trueCount + 1
        
        if trueCount >= offsetEvaluate:
            totalStatus = True
            adjustH = H.copy()
            adjustH[0, 2] = 0
            adjustH[1, 2] = 0
            adjustH[2, 0] = 0
            adjustH[2, 1] = 0
            return (totalStatus, [np.round(np.array(H).astype(np.int64)[1,2]) * (-1), 
                                 np.round(np.array(H).astype(np.int64)[0,2]) * (-1)], adjustH)
        else:
            return (totalStatus, [0, 0], 0)

    def detectAndDescribe(self, image, featureMethod):
        """
        计算图像的特征点集合，并返回该点集＆描述特征
        :param image: 需要分析的图像
        :param featureMethod: 特征检测方法 "sift", "surf", "orb"
        :return: 返回特征点集，及对应的描述特征(kps, features)
        """
        # 只使用CPU模式
        if featureMethod == "sift":
            descriptor = cv2.SIFT_create()
        elif featureMethod == "surf":
            descriptor = cv2.xfeatures2d.SURF_create()
        elif featureMethod == "orb":
            descriptor = cv2.ORB_create(
                self.orbNfeatures, self.orbScaleFactor, self.orbNlevels, 
                self.orbEdgeThreshold, self.orbFirstLevel, self.orbWTA_K, 
                0, self.orbPatchSize, self.orbFastThreshold
            )
        
        # 检测特征点，并计算描述子
        kps, features = descriptor.detectAndCompute(image, None)
        # 将结果转换成NumPy数组
        kps = np.float32([kp.pt for kp in kps])
        
        return (kps, features)

    def matchDescriptors(self, featuresA, featuresB):
        """
        匹配特征点
        :param featuresA: 第一张图像的特征点描述符
        :param featuresB: 第二张图像的特征点描述符
        :return: 返回匹配的对数matches
        """
        # 建立暴力匹配器
        if self.featureMethod == "surf" or self.featureMethod == "sift":
            matcher = cv2.DescriptorMatcher_create("BruteForce")
            # 使用KNN检测来自A、B图的特征匹配对，K=2，返回一个列表
            rawMatches = matcher.knnMatch(featuresA, featuresB, 2)
            matches = []
            for m in rawMatches:
                # 当最近距离跟次近距离的比值小于ratio值时，保留此匹配对
                if len(m) == 2 and m[0].distance < m[1].distance * self.searchRatio:
                    # 存储两个点在featuresA, featuresB中的索引值
                    matches.append((m[0].trainIdx, m[0].queryIdx))
        elif self.featureMethod == "orb":
            matcher = cv2.DescriptorMatcher_create("BruteForce-Hamming")
            rawMatches = matcher.match(featuresA, featuresB)
            matches = []
            for m in rawMatches:
                matches.append((m.trainIdx, m.queryIdx))
        
        return matches


class ImageFusion(ImageUtility):
    """图像融合类，包含多种融合算法"""
    
    def __init__(self):
        super().__init__()
        self.isColorMode = False

    def fuseByAverage(self, images):
        """
        均值融合
        :param images: 输入两个相同区域的图像
        :return: 融合后的图像
        """
        (imageA, imageB) = images
        # 由于相加后数值可能溢出，需要转变类型
        fuseRegion = np.uint8((imageA.astype(int) + imageB.astype(int)) / 2)
        return fuseRegion

    def fuseByMaximum(self, images):
        """
        最大值融合
        :param images: 输入两个相同区域的图像
        :return: 融合后的图像
        """
        (imageA, imageB) = images
        fuseRegion = np.maximum(imageA, imageB)
        return fuseRegion

    def fuseByMinimum(self, images):
        """
        最小值融合
        :param images: 输入两个相同区域的图像
        :return: 融合后的图像
        """
        (imageA, imageB) = images
        fuseRegion = np.minimum(imageA, imageB)
        return fuseRegion

    def getWeightsMatrix(self, images):
        """
        获取权值矩阵
        :param images: 输入两个相同区域的图像
        :return: weightA, weightB
        """
        (imageA, imageB) = images
        weightMatA = np.ones(imageA.shape, dtype=np.float32)
        weightMatB = np.ones(imageA.shape, dtype=np.float32)
        row, col = imageA.shape[:2]
        weightMatB_1 = weightMatB.copy()
        weightMatB_2 = weightMatB.copy()

        # 获取四条线的相加和，判断属于哪种模式
        compareList = []
        compareList.append(np.count_nonzero(imageA[0: row // 2, 0: col // 2] > 0))
        compareList.append(np.count_nonzero(imageA[row // 2: row, 0: col // 2] > 0))
        compareList.append(np.count_nonzero(imageA[row // 2: row, col // 2: col] > 0))
        compareList.append(np.count_nonzero(imageA[0: row // 2, col // 2: col] > 0))

        index = compareList.index(min(compareList))

        if index == 2:
            # 重合区域在imageA的上左部分
            rowIndex = 0
            colIndex = 0
            for j in range(1, col):
                for i in range(row - 1, -1, -1):
                    if (self.isColorMode and imageA[i, col - j].sum() != -3) or (self.isColorMode is False and imageA[i, col - j] != -1):
                        rowIndex = i + 1
                        break
                if rowIndex != 0:
                    break
            for i in range(col - 1, -1, -1):
                if (self.isColorMode and imageA[rowIndex, i].sum() != -3) or (self.isColorMode is False and imageA[rowIndex, i] != -1):
                    colIndex = i + 1
                    break
            # 赋值
            for i in range(rowIndex + 1):
                if rowIndex == 0:
                    rowIndex = 1
                weightMatB_1[rowIndex - i, :] = (rowIndex - i) * 1 / rowIndex
            for i in range(colIndex + 1):
                if colIndex == 0:
                    colIndex = 1
                weightMatB_2[:, colIndex - i] = (colIndex - i) * 1 / colIndex
            weightMatB = weightMatB_1 * weightMatB_2
            weightMatA = 1 - weightMatB
        elif index == 3:
            # 重合区域在imageA的下左部分
            rowIndex = 0
            colIndex = 0
            for j in range(1, col):
                for i in range(row):
                    if (self.isColorMode and imageA[i, col - j].sum() != -3) or (self.isColorMode is False and imageA[i, col - j] != -1):
                        rowIndex = i - 1
                        break
                if rowIndex != 0:
                    break
            for i in range(col - 1, -1, -1):
                if (self.isColorMode and imageA[rowIndex, i].sum() != -3) or (self.isColorMode is False and imageA[rowIndex, i] != -1):
                    colIndex = i + 1
                    break
            # 赋值
            for i in range(rowIndex, row):
                if rowIndex == 0:
                    rowIndex = 1
                weightMatB_1[i, :] = (row - i - 1) * 1 / (row - rowIndex - 1)
            for i in range(colIndex + 1):
                if colIndex == 0:
                    colIndex = 1
                weightMatB_2[:, colIndex - i] = (colIndex - i) * 1 / colIndex
            weightMatB = weightMatB_1 * weightMatB_2
            weightMatA = 1 - weightMatB
        elif index == 0:
            # 重合区域在imageA的下右部分
            rowIndex = 0
            colIndex = 0
            for j in range(0, col):
                for i in range(row):
                    if (self.isColorMode and imageA[i, j].sum() != -3) or (self.isColorMode is False and imageA[i, j] != -1):
                        rowIndex = i - 1
                        break
                if rowIndex != 0:
                    break
            for i in range(col):
                if (self.isColorMode and imageA[rowIndex, i].sum() != -3) or (self.isColorMode is False and imageA[rowIndex, i] != -1):
                    colIndex = i - 1
                    break
            # 赋值
            for i in range(rowIndex, row):
                if rowIndex == 0:
                    rowIndex = 1
                weightMatB_1[i, :] = (row - i - 1) * 1 / (row - rowIndex - 1)
            for i in range(colIndex, col):
                if colIndex == 0:
                    colIndex = 1
                weightMatB_2[:, i] = (col - i - 1) * 1 / (col - colIndex - 1)
            weightMatB = weightMatB_1 * weightMatB_2
            weightMatA = 1 - weightMatB
        elif index == 1:
            # 重合区域在imageA的上右部分
            rowIndex = 0
            colIndex = 0
            for j in range(0, col):
                for i in range(row - 1, -1, -1):
                    if (self.isColorMode and imageA[i, j].sum() != -3) or ((self.isColorMode is False) and imageA[i, j] != -1):
                        rowIndex = i + 1
                        break
                if rowIndex != 0:
                    break
            for i in range(col):
                if (self.isColorMode and imageA[rowIndex, i].sum() != -3) or ((self.isColorMode is False) and imageA[rowIndex, i] != -1):
                    colIndex = i - 1
                    break
            for i in range(rowIndex + 1):
                if rowIndex == 0:
                    rowIndex = 1
                weightMatB_1[rowIndex - i, :] = (rowIndex - i) * 1 / rowIndex
            for i in range(colIndex, col):
                if colIndex == 0:
                    colIndex = 1
                weightMatB_2[:, i] = (col - i - 1) * 1 / (col - colIndex - 1)
            weightMatB = weightMatB_1 * weightMatB_2
            weightMatA = 1 - weightMatB

        return (weightMatA, weightMatB)

    def fuseByFadeInAndFadeOut(self, images, dx, dy):
        """
        渐入渐出融合
        :param images: 输入两个相同区域的图像
        :param dx: x方向偏移量
        :param dy: y方向偏移量
        :return: 融合后的图像
        """
        (imageA, imageB) = images
        row, col = imageA.shape[:2]
        weightMatA = np.ones(imageA.shape, dtype=np.float32)
        weightMatB = np.ones(imageA.shape, dtype=np.float32)

        if np.count_nonzero(imageA > -1) / imageA.size > 0.65:
            # 如果对于imageA中，非0值占比例比较大，则认为是普通融合
            # 根据区域的行列大小来判断，如果行数大于列数，是水平方向
            if col <= row:
                for i in range(0, col):
                    if dy >= 0:
                        weightMatA[:, col - i - 1] = weightMatA[:, col - i - 1] * i * 1.0 / col
                        weightMatB[:, i] = weightMatB[:, i] * i * 1.0 / col
                    elif dy < 0:
                        weightMatA[:, col - i - 1] = weightMatA[:, col - i - 1] * (col - i) * 1.0 / col
                        weightMatB[:, i] = weightMatB[:, i] * (col - i) * 1.0 / col
            # 根据区域的行列大小来判断，如果列数大于行数，是竖直方向
            elif row < col:
                for i in range(0, row):
                    if dx <= 0:
                        weightMatA[i, :] = weightMatA[i, :] * i * 1.0 / row
                        weightMatB[row - i - 1, :] = weightMatB[row - i - 1, :] * i * 1.0 / row
                    elif dx > 0:
                        weightMatA[i, :] = weightMatA[i, :] * (row - i) * 1.0 / row
                        weightMatB[row - i - 1, :] = weightMatB[row - i - 1, :] * (row - i) * 1.0 / row
        else:
            # 如果对于imageA中，非0值占比例比较小，则认为是拐角融合
            weightMatA, weightMatB = self.getWeightsMatrix(images)

        imageA[imageA < 0] = imageB[imageA < 0]
        result = weightMatA * imageA.astype(np.int64) + weightMatB * imageB.astype(np.int64)
        result[result < 0] = 0
        result[result > 255] = 255
        fuseRegion = np.uint8(result)
        return fuseRegion

    def fuseByTrigonometric(self, images, dx, dy):
        """
        三角函数融合
        :param images: 输入两个相同区域的图像
        :param dx: x方向偏移量
        :param dy: y方向偏移量
        :return: 融合后的图像
        """
        (imageA, imageB) = images
        row, col = imageA.shape[:2]
        weightMatA = np.ones(imageA.shape, dtype=np.float64)
        weightMatB = np.ones(imageA.shape, dtype=np.float64)

        if np.count_nonzero(imageA > -1) / imageA.size > 0.65:
            # 如果对于imageA中，非0值占比例比较大，则认为是普通融合
            # 根据区域的行列大小来判断，如果行数大于列数，是水平方向
            if col <= row:
                for i in range(0, col):
                    if dy >= 0:
                        weightMatA[:, i] = weightMatA[:, i] * i * 1.0 / col
                        weightMatB[:, col - i - 1] = weightMatB[:, col - i - 1] * i * 1.0 / col
                    elif dy < 0:
                        weightMatA[:, i] = weightMatA[:, i] * (col - i) * 1.0 / col
                        weightMatB[:, col - i - 1] = weightMatB[:, col - i - 1] * (col - i) * 1.0 / col
            # 根据区域的行列大小来判断，如果列数大于行数，是竖直方向
            elif row < col:
                for i in range(0, row):
                    if dx <= 0:
                        weightMatA[i, :] = weightMatA[i, :] * i * 1.0 / row
                        weightMatB[row - i - 1, :] = weightMatB[row - i - 1, :] * i * 1.0 / row
                    elif dx > 0:
                        weightMatA[i, :] = weightMatA[i, :] * (row - i) * 1.0 / row
                        weightMatB[row - i - 1, :] = weightMatB[row - i - 1, :] * (row - i) * 1.0 / row
        else:
            # 如果对于imageA中，非0值占比例比较小，则认为是拐角融合
            weightMatA, weightMatB = self.getWeightsMatrix(images)

        weightMatA = np.power(np.sin(weightMatA * math.pi / 2), 2)
        weightMatB = 1 - weightMatA

        imageA[imageA < 0] = imageB[imageA < 0]
        result = weightMatA * imageA.astype(np.int64) + weightMatB * imageB.astype(np.int64)
        result[result < 0] = 0
        result[result > 255] = 255
        fuseRegion = np.uint8(result)
        return fuseRegion


class Stitcher(ImageUtility):
    """
    图像拼接类，包括所有跟材料显微组织图像配准相关函数
    """

    def __init__(self):
        super().__init__()
        self.isColorMode = True  # 是否为彩色图像模式
        self.direction = 1       # 拼接方向：1-上，2-左，3-下，4-右
        self.directIncre = 1     # 拼接增长方向，可以为1, 0, -1
        self.fuseMethod = "notFuse"  # 图像融合方法
        self.phaseResponseThreshold = 0.15  # 相位相关响应阈值
        self.tempImageFeature = ImageFeature()  # 临时存储图像特征
        self.imageFusion = ImageFusion()  # 图像融合对象

    def directionIncrease(self, direction):
        """
        改变拼接搜索方向，通过direction和directIncre控制，使得范围保持在[1,4]
        :param direction: 当前的方向
        :return: 更新后的方向
        """
        direction += self.directIncre
        if direction == 5:
            direction = 1
        if direction == 0:
            direction = 4
        return direction

    def calculateOffsetForFeatureSearch(self, images):
        """
        采用特征搜索计算偏移量
        :param images: [imageA, imageB] - 待拼接的两张图像
        :return: (status, offset) - (状态，偏移量)
        """
        (imageA, imageB) = images
        offset = [0, 0]
        status = False

        if self.isEnhance == True:
            if self.isClahe == True:
                clahe = cv2.createCLAHE(clipLimit=self.clipLimit, tileGridSize=(self.tileSize, self.tileSize))
                imageA = clahe.apply(imageA)
                imageB = clahe.apply(imageB)
            elif self.isClahe == False:
                imageA = cv2.equalizeHist(imageA)
                imageB = cv2.equalizeHist(imageB)

        # 获取特征点
        if self.tempImageFeature.isBreak == True:
            (kpsA, featuresA) = self.detectAndDescribe(imageA, featureMethod=self.featureMethod)
            (kpsB, featuresB) = self.detectAndDescribe(imageB, featureMethod=self.featureMethod)
            self.tempImageFeature.isBreak = False
            self.tempImageFeature.kps = kpsB
            self.tempImageFeature.feature = featuresB
        else:
            kpsA = self.tempImageFeature.kps
            featuresA = self.tempImageFeature.feature
            (kpsB, featuresB) = self.detectAndDescribe(imageB, featureMethod=self.featureMethod)
            self.tempImageFeature.isBreak = False
            self.tempImageFeature.kps = kpsB
            self.tempImageFeature.feature = featuresB

        if featuresA is not None and featuresB is not None:
            matches = self.matchDescriptors(featuresA, featuresB)
            if self.offsetCaculate == "mode":
                (status, offset) = self.getOffsetByMode(kpsA, kpsB, matches, offsetEvaluate=self.offsetEvaluate)
            elif self.offsetCaculate == "ransac":
                (status, offset, _) = self.getOffsetByRansac(kpsA, kpsB, matches, offsetEvaluate=self.offsetEvaluate)

        if status == False:
            self.tempImageFeature.isBreak = True
            return (status, "  The two images can not match")
        elif status == True:
            self.tempImageFeature.isBreak = False
            self.printAndWrite("  The offset of stitching: dx is " + str(offset[0]) + " dy is " + str(offset[1]))
            return (status, offset)

    def calculateOffsetForFeatureSearchIncre(self, images):
        """
        采用特征搜索计算偏移量-考虑增长搜索区域
        :param images: [imageA, imageB] - 待拼接的两张图像
        :return: (status, offset) - (状态，偏移量)
        """
        (imageA, imageB) = images
        offset = [0, 0]
        status = False
        maxI = (np.floor(0.5 / self.roiRatio) + 1).astype(int) + 1
        iniDirection = self.direction
        localDirection = iniDirection

        for i in range(1, maxI):
            while(True):
                roiImageA = self.getROIRegionForIncreMethod(imageA, direction=localDirection, order="first", searchRatio=i * self.roiRatio)
                roiImageB = self.getROIRegionForIncreMethod(imageB, direction=localDirection, order="second", searchRatio=i * self.roiRatio)

                if self.isEnhance == True:
                    if self.isClahe == True:
                        clahe = cv2.createCLAHE(clipLimit=self.clipLimit, tileGridSize=(self.tileSize, self.tileSize))
                        roiImageA = clahe.apply(roiImageA)
                        roiImageB = clahe.apply(roiImageB)
                    elif self.isClahe == False:
                        roiImageA = cv2.equalizeHist(roiImageA)
                        roiImageB = cv2.equalizeHist(roiImageB)

                kpsA, featuresA = self.detectAndDescribe(roiImageA, featureMethod=self.featureMethod)
                kpsB, featuresB = self.detectAndDescribe(roiImageB, featureMethod=self.featureMethod)

                if featuresA is not None and featuresB is not None:
                    matches = self.matchDescriptors(featuresA, featuresB)
                    if self.offsetCaculate == "mode":
                        (status, offset) = self.getOffsetByMode(kpsA, kpsB, matches, offsetEvaluate=self.offsetEvaluate)
                    elif self.offsetCaculate == "ransac":
                        (status, offset, _) = self.getOffsetByRansac(kpsA, kpsB, matches, offsetEvaluate=self.offsetEvaluate)

                if status:
                    break
                else:
                    localDirection = self.directionIncrease(localDirection)
                if localDirection == iniDirection:
                    break

            if status:
                if localDirection == 1:
                    offset[0] = offset[0] + imageA.shape[0] - int(i * self.roiRatio * imageA.shape[0])
                elif localDirection == 2:
                    offset[1] = offset[1] + imageA.shape[1] - int(i * self.roiRatio * imageA.shape[1])
                elif localDirection == 3:
                    offset[0] = offset[0] - (imageB.shape[0] - int(i * self.roiRatio * imageB.shape[0]))
                elif localDirection == 4:
                    offset[1] = offset[1] - (imageB.shape[1] - int(i * self.roiRatio * imageB.shape[1]))
                self.direction = localDirection
                break

        if status == False:
            return (status, "  The two images can not match")
        elif status == True:
            self.printAndWrite("  The offset of stitching: dx is " + str(offset[0]) + " dy is " + str(offset[1]))
            return (status, offset)

    def calculateOffsetForPhaseCorrleate(self, dirAddress):
        """
        采用相位相关法计算偏移量
        :param dirAddress: 图像地址列表
        :return: (status, offset) - (状态，偏移量)
        """
        (dir1, dir2) = dirAddress
        offset = [0, 0]
        status = True

        # 读取图像
        imageA = cv2.imdecode(np.fromfile(dir1, dtype=np.uint8), cv2.IMREAD_GRAYSCALE)
        imageB = cv2.imdecode(np.fromfile(dir2, dtype=np.uint8), cv2.IMREAD_GRAYSCALE)

        # 相位相关
        (offsetTemp, response) = cv2.phaseCorrelate(np.float64(imageA), np.float64(imageB))
        offset[0] = np.int64(offsetTemp[1])
        offset[1] = np.int64(offsetTemp[0])

        if response < self.phaseResponseThreshold:
            status = False

        self.printAndWrite("  The offset of stitching: dx is " + str(offset[0]) + " dy is " + str(offset[1]))
        return (status, offset)

    def flowStitch(self, fileList, caculateOffsetMethod):
        """
        序列拼接，从list的第一张拼接到最后一张，由于中间可能出现拼接失败，故记录截止文件索引
        :param fileList: 图像地址序列
        :param caculateOffsetMethod: 计算偏移量方法
        :return: ((status, endfileIndex), stitchImage) - （（拼接状态，截止文件索引），拼接结果）
        """
        self.printAndWrite("Stitching the directory which have " + str(fileList[0]))
        fileNum = len(fileList)
        offsetList = []
        describtion = ""

        # 计算小图像的偏移量
        startTime = time.time()
        status = True
        endfileIndex = 0

        for fileIndex in range(0, fileNum - 1):
            self.printAndWrite("stitching " + str(fileList[fileIndex]) + " and " + str(fileList[fileIndex + 1]))
            imageA = cv2.imdecode(np.fromfile(fileList[fileIndex], dtype=np.uint8), cv2.IMREAD_GRAYSCALE)
            imageB = cv2.imdecode(np.fromfile(fileList[fileIndex + 1], dtype=np.uint8), cv2.IMREAD_GRAYSCALE)

            if caculateOffsetMethod == self.calculateOffsetForPhaseCorrleate:
                (status, offset) = self.calculateOffsetForPhaseCorrleate([fileList[fileIndex], fileList[fileIndex + 1]])
            else:
                (status, offset) = caculateOffsetMethod([imageA, imageB])

            if status == False:
                describtion = "  " + str(fileList[fileIndex]) + " and " + str(fileList[fileIndex+1]) + " can not be stitched"
                break
            else:
                offsetList.append(offset)
                endfileIndex = fileIndex + 1

        endTime = time.time()
        self.printAndWrite("The time of registering is " + str(endTime - startTime) + "s")

        # 开始拼接和融合
        self.printAndWrite("start stitching")
        startTime = time.time()
        stitchImage = self.getStitchByOffset(fileList, offsetList)
        endTime = time.time()
        self.printAndWrite("The time of fusing is " + str(endTime - startTime) + "s")

        if status == False:
            self.printAndWrite(describtion)
        return ((status, endfileIndex), stitchImage)

    def flowStitchWithMutiple(self, fileList, caculateOffsetMethod):
        """
        多段序列拼接，从list的第一张拼接到最后一张，由于中间可能出现拼接失败，将分段拼接结果共同返回
        :param fileList: 图像地址序列
        :param caculateOffsetMethod: 计算偏移量方法
        :return: 拼接的图像list
        """
        result = []
        totalNum = len(fileList)
        startNum = 0

        while 1:
            (status, stitchResult) = self.flowStitch(fileList[startNum: totalNum], caculateOffsetMethod)
            result.append(stitchResult)
            self.tempImageFeature.isBreak = True

            if status[1] == 1:
                startNum = startNum + status[1] + 1
            else:
                startNum = startNum + status[1] + 1

            if startNum == totalNum:
                break
            if startNum == (totalNum - 1):
                if self.isColorMode:
                    result.append(cv2.imdecode(np.fromfile(fileList[startNum], dtype=np.uint8), cv2.IMREAD_COLOR))
                else:
                    result.append(cv2.imdecode(np.fromfile(fileList[startNum], dtype=np.uint8), cv2.IMREAD_GRAYSCALE))
                break
            self.printAndWrite("stitching Break, start from " + str(fileList[startNum]) + " again")
        return result

    def getStitchByOffset(self, fileList, originOffsetList):
        """
        通过偏移量列表和文件列表得到最终的拼接结果
        :param fileList: 图像列表
        :param originOffsetList: 偏移量列表
        :return: ndarray - 拼接后的图像
        """
        dxSum = dySum = 0
        imageList = []

        if self.isColorMode:
            imageList.append(cv2.imdecode(np.fromfile(fileList[0], dtype=np.uint8), cv2.IMREAD_COLOR))
        else:
            imageList.append(cv2.imdecode(np.fromfile(fileList[0], dtype=np.uint8), cv2.IMREAD_GRAYSCALE))

        resultRow = imageList[0].shape[0]         # 拼接最终结果的横轴长度
        resultCol = imageList[0].shape[1]         # 拼接最终结果的纵轴长度
        originOffsetList.insert(0, [0, 0])        # 增加第一张图像相对于最终结果的原点的偏移量

        rangeX = [[0,0] for _ in range(len(originOffsetList))]  # 记录X方向最大最小边界
        rangeY = [[0, 0] for _ in range(len(originOffsetList))] # 记录Y方向最大最小边界
        offsetList = copy.deepcopy(originOffsetList)
        rangeX[0][1] = imageList[0].shape[0]
        rangeY[0][1] = imageList[0].shape[1]

        for i in range(1, len(offsetList)):
            if self.isColorMode:
                tempImage = cv2.imdecode(np.fromfile(fileList[i], dtype=np.uint8), cv2.IMREAD_COLOR)
            else:
                tempImage = cv2.imdecode(np.fromfile(fileList[i], dtype=np.uint8), cv2.IMREAD_GRAYSCALE)

            dxSum = dxSum + offsetList[i][0]
            dySum = dySum + offsetList[i][1]

            if dxSum <= 0:
                for j in range(0, i):
                    offsetList[j][0] = offsetList[j][0] + abs(dxSum)
                    rangeX[j][0] = rangeX[j][0] + abs(dxSum)
                    rangeX[j][1] = rangeX[j][1] + abs(dxSum)
                resultRow = resultRow + abs(dxSum)
                rangeX[i][1] = resultRow
                dxSum = rangeX[i][0] = offsetList[i][0] = 0
            else:
                offsetList[i][0] = dxSum
                resultRow = max(resultRow, dxSum + tempImage.shape[0])
                rangeX[i][1] = resultRow

            if dySum <= 0:
                for j in range(0, i):
                    offsetList[j][1] = offsetList[j][1] + abs(dySum)
                    rangeY[j][0] = rangeY[j][0] + abs(dySum)
                    rangeY[j][1] = rangeY[j][1] + abs(dySum)
                resultCol = resultCol + abs(dySum)
                rangeY[i][1] = resultCol
                dySum = rangeY[i][0] = offsetList[i][1] = 0
            else:
                offsetList[i][1] = dySum
                resultCol = max(resultCol, dySum + tempImage.shape[1])
                rangeY[i][1] = resultCol

            imageList.append(tempImage)

        stitchResult = None
        if self.isColorMode:
            stitchResult = np.zeros((resultRow, resultCol, 3), np.int64) - 1
        else:
            stitchResult = np.zeros((resultRow, resultCol), np.int64) - 1

        self.printAndWrite("  The rectified offsetList is " + str(offsetList))

        for i in range(0, len(offsetList)):
            self.printAndWrite("  stitching " + str(fileList[i]))
            if i == 0:
                if self.isColorMode:
                    stitchResult[offsetList[0][0]: offsetList[0][0] + imageList[0].shape[0], offsetList[0][1]: offsetList[0][1] + imageList[0].shape[1], :] = imageList[0]
                else:
                    stitchResult[offsetList[0][0]: offsetList[0][0] + imageList[0].shape[0], offsetList[0][1]: offsetList[0][1] + imageList[0].shape[1]] = imageList[0]
            else:
                if self.fuseMethod == "notFuse":
                    if self.isColorMode:
                        stitchResult[offsetList[i][0]: offsetList[i][0] + imageList[i].shape[0], offsetList[i][1]: offsetList[i][1] + imageList[i].shape[1], :] = imageList[i]
                    else:
                        stitchResult[offsetList[i][0]: offsetList[i][0] + imageList[i].shape[0], offsetList[i][1]: offsetList[i][1] + imageList[i].shape[1]] = imageList[i]
                else:
                    minOccupyX = rangeX[i-1][0]
                    maxOccupyX = rangeX[i-1][1]
                    minOccupyY = rangeY[i-1][0]
                    maxOccupyY = rangeY[i-1][1]
                    roi_ltx = max(offsetList[i][0], minOccupyX)
                    roi_lty = max(offsetList[i][1], minOccupyY)
                    roi_rbx = min(offsetList[i][0] + imageList[i].shape[0], maxOccupyX)
                    roi_rby = min(offsetList[i][1] + imageList[i].shape[1], maxOccupyY)

                    if self.isColorMode:
                        roiImageRegionA = stitchResult[roi_ltx:roi_rbx, roi_lty:roi_rby, :].copy()
                        stitchResult[offsetList[i][0]: offsetList[i][0] + imageList[i].shape[0], offsetList[i][1]: offsetList[i][1] + imageList[i].shape[1], :] = imageList[i]
                        roiImageRegionB = stitchResult[roi_ltx:roi_rbx, roi_lty:roi_rby, :].copy()
                        stitchResult[roi_ltx:roi_rbx, roi_lty:roi_rby, :] = self.fuseImage([roiImageRegionA, roiImageRegionB], originOffsetList[i][0], originOffsetList[i][1])
                    else:
                        roiImageRegionA = stitchResult[roi_ltx:roi_rbx, roi_lty:roi_rby].copy()
                        stitchResult[offsetList[i][0]: offsetList[i][0] + imageList[i].shape[0], offsetList[i][1]: offsetList[i][1] + imageList[i].shape[1]] = imageList[i]
                        roiImageRegionB = stitchResult[roi_ltx:roi_rbx, roi_lty:roi_rby].copy()
                        stitchResult[roi_ltx:roi_rbx, roi_lty:roi_rby] = self.fuseImage([roiImageRegionA, roiImageRegionB], originOffsetList[i][0], originOffsetList[i][1])

        stitchResult[stitchResult == -1] = 0
        return stitchResult.astype(np.uint8)

    def fuseImage(self, images, dx, dy):
        """
        融合图像
        :param images: [imageA, imageB] - 待融合的两张图像
        :param dx: x方向偏移量
        :param dy: y方向偏移量
        :return: 融合后的图像区域
        """
        self.imageFusion.isColorMode = self.isColorMode
        (imageA, imageB) = images

        if self.fuseMethod != "fadeInAndFadeOut" and self.fuseMethod != "trigonometric":
            # 将各自区域中为背景的部分用另一区域填充，目的是消除背景
            # 权值为-1是为了方便渐入检出融合和三角融合计算
            imageA[imageA == -1] = 0
            imageB[imageB == -1] = 0
            imageA[imageA == 0] = imageB[imageA == 0]
            imageB[imageB == 0] = imageA[imageB == 0]

        fuseRegion = np.zeros(imageA.shape, np.uint8)

        if self.fuseMethod == "notFuse":
            fuseRegion = imageB
        elif self.fuseMethod == "average":
            fuseRegion = self.imageFusion.fuseByAverage([imageA, imageB])
        elif self.fuseMethod == "maximum":
            fuseRegion = self.imageFusion.fuseByMaximum([imageA, imageB])
        elif self.fuseMethod == "minimum":
            fuseRegion = self.imageFusion.fuseByMinimum([imageA, imageB])
        elif self.fuseMethod == "fadeInAndFadeOut":
            fuseRegion = self.imageFusion.fuseByFadeInAndFadeOut(images, dx, dy)
        elif self.fuseMethod == "trigonometric":
            fuseRegion = self.imageFusion.fuseByTrigonometric(images, dx, dy)

        return fuseRegion

    def imageSetStitch(self, projectAddress, outputAddress, fileNum, caculateOffsetMethod, startNum=1, fileExtension="jpg", outputfileExtension="jpg"):
        """
        图像集拼接方法
        :param projectAddress: 项目地址
        :param outputAddress: 输出地址
        :param fileNum: 共多少个文件
        :param caculateOffsetMethod: 计算偏移量方法
        :param startNum: 从第几个文件开始拼
        :param fileExtension: 输入文件扩展名
        :param outputfileExtension: 输出文件扩展名
        """
        for i in range(startNum, fileNum+1):
            fileAddress = projectAddress + "\\" + str(i) + "\\"
            fileList = glob.glob(fileAddress + "*." + fileExtension)
            if not os.path.exists(outputAddress):
                os.makedirs(outputAddress)
            self.outputAddress = outputAddress
            (status, result) = self.flowStitch(fileList, caculateOffsetMethod)
            self.tempImageFeature.isBreak = True
            cv2.imwrite(outputAddress + "\\stitching_result_" + str(i) + "." + outputfileExtension, result)
            if status == False:
                self.printAndWrite("stitching Failed")

    def imageSetStitchWithMutiple(self, projectAddress, outputAddress, fileNum, caculateOffsetMethod, startNum=1, fileExtension="jpg", outputfileExtension="jpg"):
        """
        图像集多段拼接方法
        :param projectAddress: 项目地址
        :param outputAddress: 输出地址
        :param fileNum: 共多少个文件
        :param caculateOffsetMethod: 计算偏移量方法
        :param startNum: 从第几个文件开始拼
        :param fileExtension: 输入文件扩展名
        :param outputfileExtension: 输出文件扩展名
        """
        for i in range(startNum, fileNum+1):
            startTime = time.time()
            fileAddress = projectAddress + "\\" + str(i) + "\\"
            fileList = glob.glob(fileAddress + "*." + fileExtension)
            if not os.path.exists(outputAddress):
                os.makedirs(outputAddress)
            self.outputAddress = outputAddress
            result = self.flowStitchWithMutiple(fileList, caculateOffsetMethod)
            self.tempImageFeature.isBreak = True

            if len(result) == 1:
                cv2.imwrite(outputAddress + "\\stitching_result_" + str(i) + "." + outputfileExtension, result[0])
            else:
                for j in range(0, len(result)):
                    cv2.imwrite(outputAddress + "\\stitching_result_" + str(i) + "_" + str(j+1) + "." + outputfileExtension, result[j])

            endTime = time.time()
            print("Time Consuming for " + fileAddress + " is " + str(endTime - startTime))

    def stitchTwoImages(self, imageA, imageB, method="feature"):
        """
        拼接两张图像的简单接口
        :param imageA: 第一张图像
        :param imageB: 第二张图像
        :param method: 拼接方法 "feature" 或 "phase"
        :return: 拼接结果
        """
        if method == "feature":
            (status, offset) = self.calculateOffsetForFeatureSearch([imageA, imageB])
        elif method == "phase":
            (offsetTemp, response) = cv2.phaseCorrelate(np.float64(imageA), np.float64(imageB))
            offset = [np.int64(offsetTemp[1]), np.int64(offsetTemp[0])]
            status = response > self.phaseResponseThreshold

        if not status:
            print("拼接失败：无法找到匹配点")
            return None

        # 创建临时文件列表进行拼接
        temp_files = ["temp_img1.jpg", "temp_img2.jpg"]
        cv2.imwrite(temp_files[0], imageA)
        cv2.imwrite(temp_files[1], imageB)

        result = self.getStitchByOffset(temp_files, [offset])

        # 清理临时文件
        for temp_file in temp_files:
            if os.path.exists(temp_file):
                os.remove(temp_file)

        return result


def real_images_example():
    """
    真实图像拼接示例（如果有真实图像的话）
    """
    print("\n=== 真实图像拼接示例 ===")
    
    # 尝试读取真实图像
    try:
        img1 = cv2.imread("s_0047.jpg")
        img2 = cv2.imread("s_0048.jpg")
        print(f"读取到图像: {img1.shape}, {img2.shape}")
        
        # 创建拼接器
        stitcher = Stitcher()
        stitcher.featureMethod = "orb"
        stitcher.isColorMode = True
        stitcher.fuseMethod = "fadeInAndFadeOut"
        stitcher.searchRatio = 0.75
        stitcher.offsetCaculate = "mode"
        stitcher.offsetEvaluate = 5  # 提高要求以获得更好的结果
        
        result = stitcher.stitchTwoImages(img1, img2, method="feature")
        
        if result is not None:
            cv2.imwrite("real_images_stitched.jpg", result)
            print("真实图像拼接成功！结果保存为: real_images_stitched.jpg")
        else:
            print("真实图像拼接失败：无法找到足够的匹配点")
            
    except Exception as e:
        print(f"真实图像拼接出现错误: {e}")

if __name__ == "__main__":
    real_images_example()
    print("\n" + "=" * 50)
    print("所有示例运行完成！")
    print("请查看生成的图像文件以查看拼接结果。")
